package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Coupon;
import com.tourism.miniprogram.entity.ActivationCode;
import com.tourism.miniprogram.mapper.CouponMapper;
import com.tourism.miniprogram.service.CouponService;
import com.tourism.miniprogram.service.ActivationCodeService;
import com.tourism.miniprogram.service.UsageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 数字门票服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class CouponServiceImpl extends ServiceImpl<CouponMapper, Coupon> implements CouponService {

    @Autowired
    private ActivationCodeService activationCodeService;

    @Autowired
    private UsageRecordService usageRecordService;

    @Override
    public List<Coupon> getCouponsByUserId(Integer userId) {
        try {
            return baseMapper.selectCouponsByUserId(userId);
        } catch (Exception e) {
            log.error("根据用户ID获取门票列表失败，userId: {}", userId, e);
            throw new RuntimeException("根据用户ID获取门票列表失败");
        }
    }

    @Override
    public List<Coupon> getCouponsByScenicId(Integer scenicId) {
        try {
            return baseMapper.selectCouponsByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取门票列表失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("根据景区ID获取门票列表失败");
        }
    }

    @Override
    public List<Coupon> getCouponsByStatus(String status) {
        try {
            return baseMapper.selectCouponsByStatus(status);
        } catch (Exception e) {
            log.error("根据状态获取门票列表失败，status: {}", status, e);
            throw new RuntimeException("根据状态获取门票列表失败");
        }
    }

    @Override
    public List<Coupon> getValidCouponsByUserAndScenic(Integer userId, Integer scenicId) {
        try {
            return baseMapper.selectValidCouponsByUserAndScenic(userId, scenicId);
        } catch (Exception e) {
            log.error("根据用户ID和景区ID获取有效门票失败，userId: {}, scenicId: {}", userId, scenicId, e);
            throw new RuntimeException("根据用户ID和景区ID获取有效门票失败");
        }
    }

    @Override
    @Transactional
    public boolean activateCoupon(Integer couponId, String activationCode) {
        try {
            // 查找门票
            Coupon coupon = getById(couponId);
            if (coupon == null) {
                log.error("门票不存在，couponId: {}", couponId);
                return false;
            }

            // 检查门票状态
            if (!"unactivated".equals(coupon.getStatus())) {
                log.error("门票状态不正确，无法激活，couponId: {}, status: {}", couponId, coupon.getStatus());
                return false;
            }

            // 查找激活码
            ActivationCode activationCodeEntity = activationCodeService.getByCode(activationCode);
            if (activationCodeEntity == null) {
                log.error("激活码不存在，code: {}", activationCode);
                return false;
            }

            // 检查激活码状态
            if (!"unused".equals(activationCodeEntity.getStatus())) {
                log.error("激活码已被使用或无效，code: {}, status: {}", activationCode, activationCodeEntity.getStatus());
                return false;
            }

            // 景区ID验证已移除，因为门票不再关联特定景区

            // 激活门票
            coupon.setStatus("active");
            coupon.setActivationCodeId(activationCodeEntity.getId());
            coupon.setValidFrom(LocalDateTime.now());
            // 设置过期时间（假设激活后24小时有效）
            coupon.setValidTo(LocalDateTime.now().plusHours(24));

            // 更新激活码状态
            activationCodeEntity.setStatus("used");
            activationCodeEntity.setCouponId(couponId);
            activationCodeEntity.setUsedAt(LocalDateTime.now());

            // 保存更新
            boolean couponUpdated = updateById(coupon);
            boolean codeUpdated = activationCodeService.updateById(activationCodeEntity);

            if (couponUpdated && codeUpdated) {
                log.info("门票激活成功，couponId: {}, activationCode: {}", couponId, activationCode);
                return true;
            } else {
                log.error("门票激活失败，数据库更新失败");
                return false;
            }

        } catch (Exception e) {
            log.error("激活门票失败，couponId: {}, activationCode: {}", couponId, activationCode, e);
            throw new RuntimeException("激活门票失败");
        }
    }

    @Override
    @Transactional
    public boolean useCoupon(Integer couponId, Integer userId, Integer scenicId) {
        try {
            // 查找门票
            Coupon coupon = getById(couponId);
            if (coupon == null) {
                log.error("门票不存在，couponId: {}", couponId);
                return false;
            }

            // 检查门票状态
            if (!"active".equals(coupon.getStatus())) {
                log.error("门票状态不正确，无法使用，couponId: {}, status: {}", couponId, coupon.getStatus());
                return false;
            }

            // 检查门票是否属于该用户
            if (!coupon.getUserId().equals(userId)) {
                log.error("门票不属于该用户，couponId: {}, userId: {}, couponUserId: {}", 
                         couponId, userId, coupon.getUserId());
                return false;
            }

            // 景区ID验证已移除，门票现在可以在任何景区使用

            // 检查门票是否过期
            if (coupon.getValidTo() != null && coupon.getValidTo().isBefore(LocalDateTime.now())) {
                log.error("门票已过期，couponId: {}, validTo: {}", couponId, coupon.getValidTo());
                return false;
            }

            // 使用门票
            coupon.setStatus("used");
            coupon.setUsedAt(LocalDateTime.now());

            // 创建使用记录
            boolean recordCreated = usageRecordService.createUsageRecord(couponId, userId, scenicId);
            boolean couponUpdated = updateById(coupon);

            if (couponUpdated && recordCreated) {
                log.info("门票使用成功，couponId: {}, userId: {}, scenicId: {}", couponId, userId, scenicId);
                return true;
            } else {
                log.error("门票使用失败，数据库更新失败");
                return false;
            }

        } catch (Exception e) {
            log.error("使用门票失败，couponId: {}, userId: {}, scenicId: {}", couponId, userId, scenicId, e);
            throw new RuntimeException("使用门票失败");
        }
    }

    @Override
    public String generateCouponCode() {
        // 生成唯一的门票码
        return "CPN" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
