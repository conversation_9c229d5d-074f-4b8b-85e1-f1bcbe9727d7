package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Coupon;
import com.tourism.miniprogram.service.CouponService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数字门票控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/coupons")
@Api(tags = "数字门票管理")
public class CouponController {

    @Autowired
    private CouponService couponService;

    /**
     * 分页获取门票列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取门票列表", notes = "分页获取门票列表，支持按编码、用户ID、状态筛选")
    public Result<IPage<Coupon>> getCouponPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "卡券编码") @RequestParam(required = false) String code,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "状态") @RequestParam(required = false) String status) {
        try {
            Page<Coupon> page = new Page<>(current, size);
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(code)) {
                queryWrapper.like("code", code);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("id");

            IPage<Coupon> couponPage = couponService.page(page, queryWrapper);
            return Result.success(couponPage);
        } catch (Exception e) {
            log.error("分页获取门票列表失败", e);
            return Result.error("获取门票列表失败");
        }
    }

    /**
     * 获取门票列表
     */
    @GetMapping
    @ApiOperation(value = "获取门票列表", notes = "获取所有门票列表")
    public Result<List<Coupon>> getCoupons() {
        try {
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("id");
            List<Coupon> coupons = couponService.list(queryWrapper);
            return Result.success(coupons);
        } catch (Exception e) {
            log.error("获取门票列表失败", e);
            return Result.error("获取门票列表失败");
        }
    }

    /**
     * 获取门票详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取门票详情", notes = "根据ID获取门票详情")
    public Result<Coupon> getCouponById(@ApiParam(value = "门票ID", required = true) @PathVariable Integer id) {
        try {
            Coupon coupon = couponService.getById(id);
            if (coupon == null) {
                return Result.error(404, "门票不存在");
            }
            return Result.success(coupon);
        } catch (Exception e) {
            log.error("获取门票详情失败，id: {}", id, e);
            return Result.error("获取门票详情失败");
        }
    }

    /**
     * 根据编码获取门票
     */
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码获取门票", notes = "根据卡券编码获取门票信息")
    public Result<Coupon> getCouponByCode(@ApiParam(value = "卡券编码", required = true) @PathVariable String code) {
        try {
            QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", code);
            Coupon coupon = couponService.getOne(queryWrapper);
            if (coupon == null) {
                return Result.error(404, "门票不存在");
            }
            return Result.success(coupon);
        } catch (Exception e) {
            log.error("根据编码获取门票失败，code: {}", code, e);
            return Result.error("获取门票失败");
        }
    }

    /**
     * 创建门票
     */
    @PostMapping
    @ApiOperation(value = "创建门票", notes = "创建新的门票")
    public Result<Coupon> createCoupon(@RequestBody @Valid Coupon coupon) {
        try {
            boolean success = couponService.save(coupon);
            if (success) {
                // 重新查询获取完整的门票信息（包含自动生成的ID和时间戳）
                Coupon createdCoupon = couponService.getById(coupon.getId());
                return Result.success(createdCoupon);
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建门票失败", e);
            return Result.error("创建门票失败");
        }
    }

    /**
     * 更新门票
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新门票", notes = "根据ID更新门票信息")
    public Result<String> updateCoupon(
            @ApiParam(value = "门票ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Coupon coupon) {
        try {
            coupon.setId(id);
            boolean success = couponService.updateById(coupon);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新门票失败，id: {}", id, e);
            return Result.error("更新门票失败");
        }
    }

    /**
     * 删除门票
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除门票", notes = "根据ID删除门票")
    public Result<String> deleteCoupon(@ApiParam(value = "门票ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = couponService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除门票失败，id: {}", id, e);
            return Result.error("删除门票失败");
        }
    }
}
