package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Coupon;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数字门票Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface CouponMapper extends BaseMapper<Coupon> {

    /**
     * 根据用户ID获取门票列表
     *
     * @param userId 用户ID
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByUserId(Integer userId);

    /**
     * 根据景区ID获取门票列表
     *
     * @param scenicId 景区ID
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE scenic_id = #{scenicId} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByScenicId(Integer scenicId);

    /**
     * 根据状态获取门票列表
     *
     * @param status 门票状态
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE status = #{status} ORDER BY created_at DESC")
    List<Coupon> selectCouponsByStatus(String status);

    /**
     * 根据用户ID和景区ID获取有效门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 门票列表
     */
    @Select("SELECT * FROM coupons WHERE user_id = #{userId} AND scenic_id = #{scenicId} AND status IN ('active', 'unactivated') ORDER BY created_at DESC")
    List<Coupon> selectValidCouponsByUserAndScenic(Integer userId, Integer scenicId);
}
